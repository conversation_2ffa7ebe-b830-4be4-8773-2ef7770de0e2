---
type: "always_apply"
---

<Analyzing_Rules>
以下Rules适用于波形数据分析相关功能（"sweeper400\sweeper400\analyze"目录下）的开发。
- 程序中的物理量默认使用国际单位制(SI)标准单位。
- 程序中的时间戳默认使用np.datetime64对象表示（纳秒精度）。例如，你可以使用np.datetime64("now", "ns")。

"my_dtypes.py"模块用于定义本项目中特有的自定义数据类型/容器。它主要定义了以下内容：
- "PositiveInt"类型。其用于供pyright方便地检查一个参数是否是正整数。
- "SamplingInfo"类型。这是本项目中通用的采样信息类型，pyright可方便地检查它。
- "Waveform"类。该类是numpy.ndarray的子类，是对nidaqmx包输出和输入的“时域波形数据”（我们的主要操作对象）的封装。它的核心数据是一个numpy array（dtype=numpy.float64），其中每个数据都对应一个sample的具体采样值。单通道情形下，它是一维数组（shape=(samples,)）；多通道情形下，它是二维数组（shape=(channels, samples)）。在创建Waveform对象时，必须给出希望封装的原始ndarray和sampling_rate参数。

- "basic_sine.py"模块用于包含与最简单的单频正弦波相关的类/函数。

</Analyzing_Rules>